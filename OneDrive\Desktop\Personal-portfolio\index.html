<!DOCTYPE html>
<html lang="en" ng-app="portfolioApp">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Portfolio</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
</head>
<body ng-controller="MainController" ng-class="{'dark-theme': isDarkMode}">

    <!-- Custom Dual Neon Cursor -->
    <div class="cursor-dot" id="cursorDot"></div>
    <div class="cursor-ring" id="cursorRing"></div>
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top" ng-class="{'navbar-dark': isDarkMode, 'navbar-light': !isDarkMode}">
        <div class="container">
            <a class="navbar-brand" href="#home">Rizwan</a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#projects">Projects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#skills">Skills</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-outline-primary ms-2 theme-toggle" ng-click="toggleTheme()">
                            <i class="fas" ng-class="{'fa-moon': !isDarkMode, 'fa-sun': isDarkMode}"></i>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <section id="home" class="hero-section">
        <header-component></header-component>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects-section">
        <projects-component></projects-component>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills-section">
        <skills-component></skills-component>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <contact-component></contact-component>
    </section>

    <!-- Footer -->
    <footer class="footer-section">
        <footer-component></footer-component>
    </footer>

    <!-- AngularJS 1.7 -->
    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.7.9/angular.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="js/cursor.js"></script>
    <script src="js/app.js"></script>
    <script src="js/controllers/main.controller.js"></script>
    <script src="js/services/theme.service.js"></script>
    <script src="js/components/header.component.js"></script>
    <script src="js/components/projects.component.js"></script>
    <script src="js/components/skills.component.js"></script>
    <script src="js/components/contact.component.js"></script>
    <script src="js/components/footer.component.js"></script>
    <script src="js/directives/typewriter.directive.js"></script>
</body>
</html>
