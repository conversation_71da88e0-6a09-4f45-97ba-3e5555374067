/* Custom Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    /* Light Theme */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --shadow: rgba(0, 0, 0, 0.1);
}

/* Dark Theme Variables */
.dark-theme {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: #404040;
    --shadow: rgba(255, 255, 255, 0.1);
}

/* Dual Neon Cursor System */
.cursor-dot {
    position: fixed;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background: #007bff;
    border-radius: 50%;
    pointer-events: none;
    z-index: 10001;
    transition: background 0.2s ease, box-shadow 0.2s ease;
    box-shadow:
        0 0 5px #007bff,
        0 0 10px #007bff;
    animation: dotGlow 1.5s ease-in-out infinite alternate;
}

@keyframes dotGlow {
    0% {
        box-shadow:
            0 0 5px #007bff,
            0 0 10px #007bff;
    }
    100% {
        box-shadow:
            0 0 8px #007bff,
            0 0 15px #007bff,
            0 0 20px #007bff;
    }
}

.cursor-ring {
    position: fixed;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    border: 2px solid #007bff;
    border-radius: 50%;
    pointer-events: none;
    z-index: 10000;
    background: transparent;
    transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
        0 0 10px #007bff,
        0 0 20px #007bff,
        0 0 30px #007bff,
        inset 0 0 10px rgba(0, 123, 255, 0.2);
    filter: blur(0.5px);
    animation: ringPulse 2s ease-in-out infinite;
}

@keyframes ringPulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

/* Hover states for interactive elements */
.cursor-dot.hover {
    background: #0056b3;
    box-shadow:
        0 0 8px #0056b3,
        0 0 15px #0056b3;
}

.cursor-ring.hover {
    border-color: #0056b3;
    transform: scale(1.5);
    box-shadow:
        0 0 15px #0056b3,
        0 0 25px #0056b3,
        0 0 35px #0056b3,
        inset 0 0 15px rgba(0, 86, 179, 0.3);
}

/* Theme adaptations */
.dark-theme .cursor-dot {
    background: #4dabf7;
    box-shadow:
        0 0 8px #4dabf7,
        0 0 15px #4dabf7;
}

.dark-theme .cursor-ring {
    border-color: #4dabf7;
    box-shadow:
        0 0 12px #4dabf7,
        0 0 24px #4dabf7,
        0 0 36px #4dabf7,
        inset 0 0 12px rgba(77, 171, 247, 0.25);
}

.dark-theme .cursor-dot.hover {
    background: #1c7ed6;
    box-shadow:
        0 0 10px #1c7ed6,
        0 0 20px #1c7ed6;
}

.dark-theme .cursor-ring.hover {
    border-color: #1c7ed6;
    box-shadow:
        0 0 18px #1c7ed6,
        0 0 30px #1c7ed6,
        0 0 42px #1c7ed6,
        inset 0 0 18px rgba(28, 126, 214, 0.35);
}

/* Hide default cursor on desktop only */
@media (hover: hover) and (pointer: fine) {
    * {
        cursor: none !important;
    }

    .cursor-dot,
    .cursor-ring {
        display: block;
    }
}

/* Show default cursor on touch devices */
@media (hover: none) or (pointer: coarse) {
    * {
        cursor: auto !important;
    }

    .cursor-dot,
    .cursor-ring {
        display: none !important;
    }
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    scroll-behavior: smooth;
}

/* Interactive Elements - cursor handled by JavaScript */

/* Navigation */
.navbar {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 10px var(--shadow);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.nav-link {
    color: var(--text-primary) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.theme-toggle {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

/* Animated Background Elements */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Background Circles */
.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 123, 255, 0.1);
    border: 2px solid rgba(0, 123, 255, 0.2);
    animation: float 6s ease-in-out infinite;
    will-change: transform;
}

.bg-circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.bg-circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.bg-circle-3 {
    width: 100px;
    height: 100px;
    top: 30%;
    left: 70%;
    animation-delay: 4s;
}

/* Background Blobs */
.bg-blob {
    position: absolute;
    background: linear-gradient(45deg, rgba(0, 123, 255, 0.15), rgba(0, 123, 255, 0.05));
    border-radius: 50% 40% 60% 30%;
    animation: morph 8s ease-in-out infinite;
    filter: blur(1px);
    will-change: transform;
}

.bg-blob-1 {
    width: 300px;
    height: 250px;
    top: 20%;
    right: 10%;
    animation-delay: 1s;
}

.bg-blob-2 {
    width: 250px;
    height: 200px;
    bottom: 20%;
    left: 5%;
    animation-delay: 3s;
}

/* Gradient Orbs */
.gradient-orb {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 123, 255, 0.2) 0%, rgba(0, 123, 255, 0.05) 50%, transparent 100%);
    animation: pulse 4s ease-in-out infinite;
    filter: blur(2px);
    will-change: transform;
}

.gradient-orb-1 {
    width: 400px;
    height: 400px;
    top: -10%;
    right: -10%;
    animation-delay: 0.5s;
}

.gradient-orb-2 {
    width: 350px;
    height: 350px;
    bottom: -15%;
    left: -15%;
    animation-delay: 2.5s;
}

/* Dark Theme Adaptations */
.dark-theme .bg-circle {
    background: rgba(77, 171, 247, 0.1);
    border-color: rgba(77, 171, 247, 0.2);
}

.dark-theme .bg-blob {
    background: linear-gradient(45deg, rgba(77, 171, 247, 0.15), rgba(77, 171, 247, 0.05));
}

.dark-theme .gradient-orb {
    background: radial-gradient(circle, rgba(77, 171, 247, 0.2) 0%, rgba(77, 171, 247, 0.05) 50%, transparent 100%);
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes morph {
    0%, 100% {
        border-radius: 50% 40% 60% 30%;
        transform: rotate(0deg);
    }
    25% {
        border-radius: 30% 60% 40% 50%;
        transform: rotate(90deg);
    }
    50% {
        border-radius: 60% 30% 50% 40%;
        transform: rotate(180deg);
    }
    75% {
        border-radius: 40% 50% 30% 60%;
        transform: rotate(270deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

/* Projects Section */
.projects-section {
    padding: 5rem 0;
    background-color: var(--bg-secondary);
}

/* Skills Section */
.skills-section {
    padding: 5rem 0;
    background-color: var(--bg-primary);
}

/* Contact Section */
.contact-section {
    padding: 5rem 0;
    background-color: var(--bg-secondary);
}

/* Footer */
.footer-section {
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: 2rem 0 1rem;
}

/* Cards */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 15px var(--shadow);
    transition: all 0.3s ease;
    border-radius: 15px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow);
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease forwards;
}

.fade-in {
    animation: fadeIn 0.6s ease forwards;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 1rem;
        text-align: center;
    }

    .projects-section, .skills-section, .contact-section {
        padding: 3rem 0;
    }

    .navbar-brand {
        font-size: 1.25rem;
    }

    .btn {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }

    .skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .skill-item {
        padding: 1.5rem 1rem;
    }

    .contact-form-container {
        padding: 2rem 1.5rem;
    }

    .social-links {
        gap: 0.5rem;
    }

    .social-link {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        min-height: 90vh;
    }

    .card {
        margin-bottom: 1.5rem;
    }

    .profile-image {
        width: 250px;
        height: 250px;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .contact-form-container {
        padding: 1.5rem 1rem;
    }

    .skill-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    /* Reduce background elements on mobile */
    .bg-circle {
        opacity: 0.5;
    }

    .bg-circle-1 {
        width: 120px;
        height: 120px;
    }

    .bg-circle-2 {
        width: 100px;
        height: 100px;
    }

    .bg-circle-3 {
        width: 80px;
        height: 80px;
    }

    .bg-blob-1 {
        width: 200px;
        height: 150px;
    }

    .bg-blob-2 {
        width: 180px;
        height: 120px;
    }

    .gradient-orb-1 {
        width: 250px;
        height: 250px;
    }

    .gradient-orb-2 {
        width: 200px;
        height: 200px;
    }
}

/* Smooth Scrolling Enhancement */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.loading {
    opacity: 0;
    animation: fadeIn 0.5s ease 0.2s forwards;
}

/* Header/Profile Styles */
.profile-image-container {
    position: relative;
    display: inline-block;
    margin: 2rem 0;
}

.profile-image {
    width: 300px;
    height: 300px;
    object-fit: cover;
    border: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.profile-image:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px var(--shadow);
}

/* Typewriter Animation */
.typewriter-text {
    position: relative;
    display: inline-block;
}

.typewriter-text::after {
    content: '|';
    display: inline-block;
    color: var(--primary-color);
    margin-left: 3px;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* Skills Section Styles */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.skill-item {
    text-align: center;
    padding: 2rem 1rem;
    background-color: var(--bg-secondary);
    border-radius: 15px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.skill-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow);
}

.skill-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.skill-name {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.skill-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

.additional-skills {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
}

.skill-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skill-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px var(--shadow);
}

/* Contact Section Styles */
.contact-form-container {
    background-color: var(--bg-primary);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px var(--shadow);
    border: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    background-color: var(--bg-secondary);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control::placeholder {
    color: var(--text-secondary);
}

/* Form Validation Styles */
.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

/* Alert Styles */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.5rem;
    position: relative;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.dark-theme .alert-success {
    color: #d4edda;
    background-color: rgba(40, 167, 69, 0.2);
    border-color: rgba(40, 167, 69, 0.3);
}

.dark-theme .alert-danger {
    color: #f8d7da;
    background-color: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.3);
}

/* Button disabled state */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Character counter */
.form-text {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.contact-info-item {
    padding: 2rem 1rem;
}

.contact-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.contact-info-item h5 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.contact-info-item p {
    color: var(--text-secondary);
    margin: 0;
}

/* Social Links */
.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: var(--bg-primary);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    font-size: 1.25rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px var(--shadow);
}

/* Project Card Styles */
.project-card {
    overflow: hidden;
    transition: all 0.3s ease;
}

.card-img-container {
    position: relative;
    overflow: hidden;
}

.project-image {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-tech {
    text-align: center;
}

.project-links .btn {
    margin-bottom: 0.5rem;
}

@media (max-width: 576px) {
    .project-links .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
        margin-right: 0 !important;
    }
}

/* Text Color Fixes for Dark Mode */
.text-muted {
    color: var(--text-secondary) !important;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
}

p {
    color: var(--text-primary);
}

.lead {
    color: var(--text-secondary) !important;
}

/* Social Icons */
.social-icon {
    font-size: 1.5rem;
    margin: 0 0.5rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.social-icon:hover {
    color: var(--primary-color);
    transform: translateY(-3px);
}
